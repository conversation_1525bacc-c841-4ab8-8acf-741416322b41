import { Injectable, Inject, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import { CreatePostDto } from './dto/create-post.dto';
import { PostResponseDto, PostFeedResponseDto } from './dto/post.dto';
import { CreateCommentDto, CommentResponseDto } from './dto/comment.dto';
import { GetPostFeedDto } from './dto/feeds.dto';
import { AdminGetPostsDto } from './dto/admin.dto';
import { PostStatsEventDto, PostStatsResponseDto } from './dto/post-stats.dto';
import { PostCacheService } from './services/post-cache.service';

@Injectable()
export class PostService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly cacheService: PostCacheService,
    ) {
        this.logger.setContext(PostService.name);
    }

    /**
     * 创建帖子
     * @param userId 用户ID
     * @param createPostDto 创建帖子DTO
     * @returns 创建的帖子信息
     */
    async createPost(userId: string, createPostDto: CreatePostDto): Promise<PostResponseDto> {
        const taskId = createPostDto.taskId;

        const { data: video, error: videoError } = await this.supabase.from('videos').select('*').eq('task_id', taskId).single();

        if (videoError) {
            this.logger.error('获取视频失败', videoError);
            throw new NotFoundException('任务不存在');
        }

        // 创建帖子
        const { data, error: createError } = await this.supabase
            .from('posts')
            .insert({
                user_id: userId,
                title: createPostDto.title,
                description: createPostDto.description,
                thumbnail_url: createPostDto.thumbnailUrl,
                task_id: taskId,
                video_id: video.id,
                video_url: video.url,
            })
            .select('*')
            .single();

        if (createError) {
            this.logger.error('创建帖子失败', createError);
            throw new BadRequestException('创建帖子失败');
        }

        return data;
    }

    /**
     * 获取帖子Feed流 - 支持多种推荐策略
     * @param getPostFeedDto 包含分页参数、标签和推荐策略
     * @returns 帖子列表和总数
     */
    async getPostFeed({
        limit = 10,
        offset = 0,
        tag_id = '',
        strategy = 'mixed',
        hot_ratio = 0.7,
        hot_days_range = 30
    }: GetPostFeedDto): Promise<PostFeedResponseDto> {
        try {
            // 参数验证和默认值设置
            const validatedLimit = Math.min(Math.max(limit, 1), 50);
            const validatedOffset = Math.max(offset, 0);
            const validatedHotRatio = Math.min(Math.max(hot_ratio || 0.7, 0), 1);
            const validatedDaysRange = Math.min(Math.max(hot_days_range || 30, 1), 365);

            // 如果指定了标签，使用标签筛选
            if (tag_id && tag_id !== 'all') {
                return await this.getPostsByTag(tag_id, validatedLimit, validatedOffset);
            }

            // 生成缓存键
            const cacheKey = this.cacheService.generateFeedCacheKey(
                strategy,
                validatedLimit,
                validatedOffset,
                validatedHotRatio,
                tag_id
            );

            // 尝试从缓存获取
            const cachedResult = this.cacheService.getFeedCache(cacheKey);
            if (cachedResult) {
                this.logger.debug(`Feed缓存命中: ${cacheKey}`);
                return cachedResult;
            }

            // 根据策略选择不同的获取方法
            let result: PostFeedResponseDto;
            switch (strategy) {
                case 'hot':
                    result = await this.getHotPosts(validatedLimit, validatedOffset, validatedDaysRange);
                    break;

                case 'latest':
                    result = await this.getLatestPosts(validatedLimit, validatedOffset);
                    break;

                case 'mixed':
                default:
                    result = await this.getMixedPosts(validatedLimit, validatedOffset, validatedHotRatio);
                    break;
            }

            // 缓存结果
            this.cacheService.setFeedCache(cacheKey, result);

            return result;
        } catch (error) {
            this.logger.error('获取帖子列表失败', error);
            throw error;
        }
    }

    /**
     * 获取混合推荐的帖子列表（热门+最新）
     * @param limit 限制数量
     * @param offset 偏移量
     * @param hotRatio 热门内容占比
     * @returns 混合推荐的帖子列表
     */
    private async getMixedPosts(limit: number, offset: number, hotRatio: number): Promise<PostFeedResponseDto> {
        try {
            const { data, error } = await this.supabase
                .rpc('get_mixed_post_feed', {
                    p_limit: limit,
                    p_offset: offset,
                    p_hot_ratio: hotRatio
                });

            if (error) {
                this.logger.warn('混合推荐RPC调用失败，使用备选方案');
                return await this.getFallbackMixedPosts(limit, offset, hotRatio);
            }

            // 获取用户信息和视频信息
            const enrichedPosts = await this.enrichPostsData(data || []);

            return {
                posts: enrichedPosts,
                total: enrichedPosts.length
            };
        } catch (error) {
            this.logger.error('获取混合推荐失败', error);
            return await this.getFallbackMixedPosts(limit, offset, hotRatio);
        }
    }

    /**
     * 获取热门帖子列表
     * @param limit 限制数量
     * @param offset 偏移量
     * @param daysRange 时间范围（天数）
     * @returns 热门帖子列表
     */
    private async getHotPosts(limit: number, offset: number, daysRange: number): Promise<PostFeedResponseDto> {
        try {
            const { data, error } = await this.supabase
                .rpc('get_hot_posts', {
                    p_limit: limit,
                    p_offset: offset,
                    p_days_range: daysRange
                });

            if (error) {
                this.logger.warn('热门推荐RPC调用失败，使用备选方案');
                return await this.getFallbackHotPosts(limit, offset);
            }

            const enrichedPosts = await this.enrichPostsData(data || []);

            return {
                posts: enrichedPosts,
                total: enrichedPosts.length
            };
        } catch (error) {
            this.logger.error('获取热门帖子失败', error);
            return await this.getFallbackHotPosts(limit, offset);
        }
    }

    /**
     * 获取最新帖子列表
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 最新帖子列表
     */
    private async getLatestPosts(limit: number, offset: number): Promise<PostFeedResponseDto> {
        try {
            const { data, error } = await this.supabase
                .rpc('get_latest_posts', {
                    p_limit: limit,
                    p_offset: offset
                });

            if (error) {
                this.logger.warn('最新内容RPC调用失败，使用备选方案');
                return await this.getFallbackLatestPosts(limit, offset);
            }

            const enrichedPosts = await this.enrichPostsData(data || []);

            return {
                posts: enrichedPosts,
                total: enrichedPosts.length
            };
        } catch (error) {
            this.logger.error('获取最新帖子失败', error);
            return await this.getFallbackLatestPosts(limit, offset);
        }
    }

    /**
     * 获取用户的帖子列表
     * @param userId 用户ID
     * @param currentUserId 当前查看的用户ID
     * @param limit 分页限制
     * @param offset 分页偏移
     * @returns 用户的帖子列表和总数
     */
    async getUserPosts(
        userId: string,
        currentUserId: string | null,
        limit: number = 10,
        offset: number = 0
    ): Promise<PostFeedResponseDto> {
        try {
            let query = this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar)', { count: 'estimated' })
                .eq('user_id', userId);

            // 如果是其他用户查看，只显示公开帖子
            if (currentUserId !== userId) {
                query = query.eq('visibility', 'public');
            }

            query = query.order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            const { data: posts, error, count } = await query;

            if (error) {
                this.logger.error('获取用户帖子列表失败', error);
                throw new BadRequestException('获取用户帖子列表失败');
            }

            return {
                posts,
                total: count || 0
            };
        } catch (error) {
            this.logger.error('获取用户帖子列表失败', error);
            throw error;
        }
    }

    /**
     * 获取帖子详情
     * @param postId 帖子ID
     * @param userId 当前用户ID
     * @returns 帖子详情
     */
    async getPostById(postId: string, userId: string | null): Promise<PostResponseDto> {
        try {
            const { data: post, error } = await this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar), videos(id, prompt, url, input_params, cover_img), likes(user_id)')
                .eq('id', postId)
                .single();

            if (error || !post) {
                this.logger.error('获取帖子详情失败', error);
                throw new NotFoundException(error.message);
            }

            // 如果不是公开帖子且不是帖子作者，则无权查看
            if (post.visibility !== 'public' && post.user_id !== userId) {
                throw new ForbiddenException('无权查看此帖子');
            }

            return post;
        } catch (error) {
            this.logger.error('获取帖子详情失败', error);
            throw error;
        }
    }

    /**
     * 点赞或取消点赞帖子
     * @param postId 帖子ID
     * @param userId 用户ID
     * @param isLike 是否点赞
     * @returns 操作后的帖子详情
     */
    async likePost(postId: string, userId: string, isLike: boolean): Promise<boolean> {
        try {
            // 首先检查帖子是否存在
            await this.getPostById(postId, userId);

            if (isLike) {
                // 添加点赞
                const { error } = await this.supabase
                    .from('likes')
                    .insert({
                        user_id: userId,
                        post_id: postId,
                    })
                    .select()
                    .single();

                if (error && error.code !== '23505') { // 忽略唯一约束冲突（已点赞）
                    this.logger.error('点赞帖子失败', error);
                    throw new BadRequestException('点赞失败');
                }
            } else {
                // 删除点赞
                const { error } = await this.supabase
                    .from('likes')
                    .delete()
                    .eq('user_id', userId)
                    .eq('post_id', postId);

                if (error) {
                    this.logger.error('取消点赞失败', error);
                    throw new BadRequestException('取消点赞失败');
                }
            }

            // 清理相关缓存
            this.cacheService.invalidateHotnessCache(postId);
            this.cacheService.invalidateFeedCache();

            // 重新获取最新的帖子信息
            return true;
        } catch (error) {
            this.logger.error(`${isLike ? '点赞' : '取消点赞'}帖子失败`, error);
            throw error;
        }
    }

    /**
     * 创建评论
     * @param userId 用户ID
     * @param createCommentDto 创建评论DTO
     * @returns 创建的评论信息
     */
    async createComment(userId: string, createCommentDto: CreateCommentDto): Promise<CommentResponseDto> {
        try {
            // 验证评论目标是否存在
            if (createCommentDto.targetType === 'post') {
                // 验证帖子是否存在
                await this.getPostById(createCommentDto.targetId, userId);
            } else if (createCommentDto.targetType === 'model') {
                // 这里可以添加验证模型是否存在的逻辑
                // TODO: 实现模型验证
            } else {
                throw new BadRequestException('无效的评论目标类型');
            }

            // 如果是回复评论，验证父评论是否存在
            if (createCommentDto.parentId) {
                const { data: parentComment, error } = await this.supabase
                    .from('comments')
                    .select('id')
                    .eq('id', createCommentDto.parentId)
                    .single();

                if (error || !parentComment) {
                    throw new NotFoundException('父评论不存在');
                }
            }

            // 创建评论
            const { data, error } = await this.supabase
                .from('comments')
                .insert({
                    user_id: userId,
                    content: createCommentDto.content,
                    parent_id: createCommentDto.parentId,
                    target_type: createCommentDto.targetType,
                    target_id: createCommentDto.targetId,
                })
                .select('*, users:user_id(nickname, avatar)')
                .single();

            if (error) {
                this.logger.error('创建评论失败', error);
                throw new BadRequestException('创建评论失败');
            }

            // 清理相关缓存（评论会影响热度计算）
            this.cacheService.invalidateHotnessCache(createCommentDto.targetId);
            this.cacheService.invalidateFeedCache();

            return data;
        } catch (error) {
            this.logger.error('创建评论失败', error);
            throw error;
        }
    }

    /**
     * 获取评论列表
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param parentId 父评论ID，如果为空则获取顶级评论
     * @param limit 分页限制
     * @param offset 分页偏移
     * @returns 评论列表和总数
     */
    async getComments(
        targetType: string,
        targetId: string,
        parentId: string | null = null,
        limit: number = 10,
        offset: number = 0
    ): Promise<{ comments: CommentResponseDto[]; total: number }> {
        try {
            let query = this.supabase
                .from('comments')
                .select('*, user_profiles(nickname, avatar)', { count: 'estimated' })
                .eq('target_type', targetType)
                .eq('target_id', targetId)
                .order('created_at', { ascending: false });

            if (parentId) {
                query = query.eq('parent_id', parentId);
            } else {
                query = query.is('parent_id', null);
            }

            query = query.range(offset, offset + limit - 1);

            const { data: comments, error, count } = await query;

            if (error) {
                this.logger.error('获取评论列表失败', error);
                throw new BadRequestException('获取评论列表失败');
            }

            // 获取每个顶级评论的回复数
            const commentIds = comments.map(comment => comment.id);
            let childrenCounts = {};

            if (commentIds.length > 0 && !parentId) {
                // 使用独立的SQL查询而不是group
                for (const commentId of commentIds) {
                    const { count: replyCount } = await this.supabase
                        .from('comments')
                        .select('*', { count: 'exact', head: true })
                        .eq('parent_id', commentId);

                    childrenCounts[commentId] = replyCount || 0;
                }
            }

            return {
                comments,
                total: count || 0
            };
        } catch (error) {
            this.logger.error('获取评论列表失败', error);
            throw error;
        }
    }

    /**
     * 管理员获取所有帖子
     * @param adminGetPostsDto 获取帖子的参数
     * @returns 所有帖子列表和总数
     */
    async getAdminPosts({
        visibility = null,
        nsfw_level = null,
        limit = 20,
        offset = 0
    }: AdminGetPostsDto): Promise<{ posts: any[], total: number }> {
        try {
            // 构建查询
            let query = this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar)', { count: 'estimated' });

            // 根据可见性筛选
            if (visibility) {
                query = query.eq('visibility', visibility);
            }

            // 根据NSFW级别筛选
            if (nsfw_level) {
                query = query.eq('nsfw_level', nsfw_level);
            }

            // 分页和排序
            query = query.order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            const { data: posts, error, count } = await query;

            if (error) {
                this.logger.error('管理员获取帖子列表失败', error);
                throw new BadRequestException('获取帖子列表失败');
            }

            return {
                posts: posts || [],
                total: count || 0
            };
        } catch (error) {
            this.logger.error('管理员获取帖子列表失败', error);
            throw error;
        }
    }

    /**
     * 管理员获取帖子详情
     * @param postId 帖子ID
     * @returns 帖子详情
     */
    async getAdminPostDetails(postId: string): Promise<any> {
        try {
            const { data: post, error } = await this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar), videos(id, prompt, url, input_params), likes(user_id)')
                .eq('id', postId)
                .single();

            if (error || !post) {
                this.logger.error('管理员获取帖子详情失败', error);
                throw new NotFoundException('帖子不存在');
            }

            return post;
        } catch (error) {
            this.logger.error('管理员获取帖子详情失败', error);
            throw error;
        }
    }

    /**
     * 管理员更新帖子可见性
     * @param postId 帖子ID
     * @param visibility 可见性
     * @returns 更新后的帖子信息
     */
    async updatePostVisibility(postId: string, visibility: string): Promise<any> {
        try {
            const { data, error } = await this.supabase
                .from('posts')
                .update({ visibility, updated_at: new Date().toISOString() })
                .eq('id', postId)
                .select()
                .single();

            if (error) {
                this.logger.error('更新帖子可见性失败', error);
                throw new BadRequestException('更新帖子可见性失败');
            }

            return data;
        } catch (error) {
            this.logger.error('更新帖子可见性失败', error);
            throw error;
        }
    }

    /**
     * 管理员更新帖子NSFW级别
     * @param postId 帖子ID
     * @param nsfwLevel NSFW级别
     * @returns 更新后的帖子信息
     */
    async updatePostNsfwLevel(postId: string, nsfwLevel: string): Promise<any> {
        try {
            const { data, error } = await this.supabase
                .from('posts')
                .update({ nsfw_level: nsfwLevel, updated_at: new Date().toISOString() })
                .eq('id', postId)
                .select()
                .single();

            if (error) {
                this.logger.error('更新帖子NSFW级别失败', error);
                throw new BadRequestException('更新帖子NSFW级别失败');
            }

            return data;
        } catch (error) {
            this.logger.error('更新帖子NSFW级别失败', error);
            throw error;
        }
    }

    /**
     * 根据标签获取帖子
     * 这个方法可以复用于其他需要按标签筛选帖子的场景
     * 使用嵌套子查询以单次请求完成查询，优化性能
     * @param tag_id 标签ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 帖子列表和总数
     */
    async getPostsByTag(tag_id: string, limit: number = 10, offset: number = 0): Promise<PostFeedResponseDto> {
        try {
            // 单次查询：使用PostgreSQL函数过滤标签相关帖子
            // 注：这里我们使用Supabase的RPC调用，避免SQL注入风险
            console.log('rpc get_posts_by_tag_id', tag_id, limit, offset);
            const { data, error } = await this.supabase
                .rpc('get_posts_by_tag_id', {
                    p_tag_id: tag_id,
                    p_limit: limit,
                    p_offset: offset
                });

            // 如果RPC函数不存在，则使用预编译的过滤条件
            if (error && error.code === '42883') { // 函数不存在
                this.logger.warn('找不到get_posts_by_tag_id函数，使用备选查询方法');

                // 先安全地获取标签ID
                const { data: tag } = await this.supabase
                    .from('tags')
                    .select('id')
                    .eq('id', tag_id)
                    .single();

                if (!tag) {
                    return { posts: [], total: 0 };
                }

                // 获取具有该标签的视频IDs
                const { data: videoTags } = await this.supabase
                    .from('video_tags')
                    .select('video_id')
                    .eq('tag_id', tag.id);

                if (!videoTags || videoTags.length === 0) {
                    return { posts: [], total: 0 };
                }

                const videoIds = videoTags.map(vt => vt.video_id);

                // 获取帖子
                const { data: posts, count } = await this.supabase
                    .from('posts')
                    .select('*, videos(id, prompt, url, input_params), user_profiles(nickname, avatar)', { count: 'estimated' })
                    .eq('visibility', 'public')
                    .in('video_id', videoIds)
                    .order('created_at', { ascending: false })
                    .range(offset, offset + limit - 1);

                return {
                    posts: posts || [],
                    total: count || 0
                };
            } else if (error) {
                this.logger.error('按标签获取帖子失败', error);
                throw new BadRequestException('获取帖子列表失败');
            }

            return data;
        } catch (error) {
            this.logger.error(`根据标签获取帖子失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * 记录帖子统计事件（浏览量或点击量）
     * @param userId 用户ID，可选
     * @param statsEventDto 统计事件DTO
     * @returns 操作结果
     */
    async recordPostStats(userId: string | null, statsEventDto: PostStatsEventDto): Promise<PostStatsResponseDto> {
        try {
            // 验证帖子是否存在
            try {
                await this.getPostById(statsEventDto.post_id, userId);
            } catch (error) {
                // 如果帖子不存在或无权访问，记录错误但不阻止操作
                this.logger.warn(`尝试记录不存在或无权访问的帖子统计: ${statsEventDto.post_id} ${error}`);
                return { success: false };
            }

            // 记录统计事件
            const { error } = await this.supabase
                .from('post_stats_logs')
                .insert({
                    post_id: statsEventDto.post_id,
                    user_id: userId,
                    event_type: statsEventDto.event_type,
                    client_info: statsEventDto.client_info || {}
                });

            if (error) {
                this.logger.error('记录帖子统计失败', error);
                return { success: false };
            }

            return { success: true };
        } catch (error) {
            this.logger.error(`记录帖子统计时发生错误: ${error.message}`, error);
            // 返回失败但不抛出异常，避免影响用户体验
            return { success: false };
        }
    }

    /**
     * 获取帖子统计数据
     * @param postId 帖子ID
     * @returns 帖子统计数据
     */
    async getPostStats(postId: string): Promise<{ view_count: number, click_count: number }> {
        try {
            const { data, error } = await this.supabase
                .from('post_stats')
                .select('view_count, click_count')
                .eq('post_id', postId)
                .single();

            if (error) {
                // 如果没有找到统计数据，返回默认值
                return { view_count: 0, click_count: 0 };
            }

            return data;
        } catch (error) {
            this.logger.error(`获取帖子统计数据失败: ${error.message}`, error);
            return { view_count: 0, click_count: 0 };
        }
    }

    /**
     * 丰富帖子数据 - 添加用户信息和视频信息
     * @param posts 原始帖子数据
     * @returns 丰富后的帖子数据
     */
    private async enrichPostsData(posts: any[]): Promise<PostResponseDto[]> {
        if (!posts || posts.length === 0) {
            return [];
        }

        try {
            // 获取所有需要的用户ID和视频ID
            const userIds = [...new Set(posts.map(p => p.user_id).filter(Boolean))];
            const videoIds = [...new Set(posts.map(p => p.video_id).filter(Boolean))];

            // 批量获取用户信息
            const { data: userProfiles } = await this.supabase
                .from('user_profiles')
                .select('user_id, nickname, avatar')
                .in('user_id', userIds);

            // 批量获取视频信息
            const { data: videos } = await this.supabase
                .from('videos')
                .select('id, prompt, url, input_params, cover_img')
                .in('id', videoIds);

            // 创建查找映射
            const userMap = new Map(userProfiles?.map(u => [u.user_id, u]) || []);
            const videoMap = new Map(videos?.map(v => [v.id, v]) || []);

            // 丰富帖子数据
            return posts.map(post => ({
                ...post,
                user_profiles: userMap.get(post.user_id) || { nickname: '未知用户', avatar: null },
                videos: post.video_id ? videoMap.get(post.video_id) : null,
                content_type: post.content_type || 'normal'
            }));
        } catch (error) {
            this.logger.error('丰富帖子数据失败', error);
            // 返回原始数据，避免完全失败
            return posts.map(post => ({
                ...post,
                user_profiles: { nickname: '未知用户', avatar: null },
                content_type: post.content_type || 'normal'
            }));
        }
    }

    /**
     * 备选混合推荐方案 - 当RPC函数不可用时使用
     * @param limit 限制数量
     * @param offset 偏移量
     * @param hotRatio 热门内容占比
     * @returns 混合推荐的帖子列表
     */
    private async getFallbackMixedPosts(limit: number, offset: number, hotRatio: number): Promise<PostFeedResponseDto> {
        try {
            const hotLimit = Math.floor(limit * hotRatio);
            const newLimit = limit - hotLimit;

            // 获取热门内容（基于点赞数）
            const { data: hotPosts } = await this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar), videos(id, prompt, url, input_params, cover_img)')
                .eq('visibility', 'public')
                .eq('is_deleted', false)
                .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // 30天内
                .order('like_count', { ascending: false })
                .order('comment_count', { ascending: false })
                .limit(hotLimit)
                .range(Math.floor(offset * hotRatio), Math.floor(offset * hotRatio) + hotLimit - 1);

            // 获取新内容（基于时间）
            const { data: newPosts } = await this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar), videos(id, prompt, url, input_params, cover_img)')
                .eq('visibility', 'public')
                .eq('is_deleted', false)
                .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // 7天内
                .order('created_at', { ascending: false })
                .limit(newLimit)
                .range(Math.floor(offset * (1 - hotRatio)), Math.floor(offset * (1 - hotRatio)) + newLimit - 1);

            // 合并结果并标记类型
            const allPosts = [
                ...(hotPosts || []).map(p => ({ ...p, content_type: 'hot' })),
                ...(newPosts || []).map(p => ({ ...p, content_type: 'new' }))
            ];

            return {
                posts: allPosts,
                total: allPosts.length
            };
        } catch (error) {
            this.logger.error('备选混合推荐失败', error);
            // 最后的备选方案：按时间排序
            return await this.getFallbackLatestPosts(limit, offset);
        }
    }

    /**
     * 备选热门推荐方案
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 热门帖子列表
     */
    private async getFallbackHotPosts(limit: number, offset: number): Promise<PostFeedResponseDto> {
        try {
            const { data: posts, error, count } = await this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar), videos(id, prompt, url, input_params, cover_img)', { count: 'exact' })
                .eq('visibility', 'public')
                .eq('is_deleted', false)
                .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // 30天内
                .order('like_count', { ascending: false })
                .order('comment_count', { ascending: false })
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error('备选热门推荐查询失败', error);
                throw new BadRequestException('获取热门帖子失败');
            }

            return {
                posts: (posts || []).map(p => ({ ...p, content_type: 'hot' })),
                total: count || 0
            };
        } catch (error) {
            this.logger.error('备选热门推荐失败', error);
            throw error;
        }
    }

    /**
     * 备选最新推荐方案
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 最新帖子列表
     */
    private async getFallbackLatestPosts(limit: number, offset: number): Promise<PostFeedResponseDto> {
        try {
            const { data: posts, error, count } = await this.supabase
                .from('posts')
                .select('*, user_profiles(nickname, avatar), videos(id, prompt, url, input_params, cover_img)', { count: 'exact' })
                .eq('visibility', 'public')
                .eq('is_deleted', false)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error('备选最新推荐查询失败', error);
                throw new BadRequestException('获取最新帖子失败');
            }

            return {
                posts: (posts || []).map(p => ({ ...p, content_type: 'new' })),
                total: count || 0
            };
        } catch (error) {
            this.logger.error('备选最新推荐失败', error);
            throw error;
        }
    }
}