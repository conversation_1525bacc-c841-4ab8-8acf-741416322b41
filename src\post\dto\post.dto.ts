export class InputParamsDto {
    seed: number;
    ratio: string;
    steps: number;
    prompt: string;
    duration: string;
    gen_type: string;
    definition: string;
    guidance_scale: number;
    negative_prompt: string;
    refer_img_url: string;
}

export class PostResponseDto {
    id: string;
    user_id: string;
    title: string;
    description: string;
    video_url: string;
    thumbnail_url: string;
    task_id: string;
    video_id: string;
    like_count: number;
    comment_count: number;
    created_at: string;
    updated_at: string;
    visibility: string;
    is_deleted: boolean;
    nsfw_level: string;
    ext: object;
    likes?: {
        user_id: string;
    }[];
    user_profiles: {
        avatar: string;
        nickname: string;
    }
    videos?: {
        id: string;
        prompt: string;
        url: string;
        input_params: InputParamsDto;
    }
    // 新增字段：热度分数和内容类型
    hotness_score?: number;
    content_type?: 'hot' | 'new' | 'normal';
    // 统计数据
    view_count?: number;
    click_count?: number;
}

export class PostFeedResponseDto {
    posts: PostResponseDto[];
    total: number;
} 