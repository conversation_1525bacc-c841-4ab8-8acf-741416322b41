"use client";

import { useState, useCallback } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSoundStore } from "@/store/useSoundStore";
import { useVoiceModels } from "../hooks/useVoiceModels";
import { Play, Pause } from "lucide-react";
import { cn } from "@/lib/utils";
import * as SliderPrimitive from "@radix-ui/react-slider";

// Enhanced ZEN slider with gradient and smooth animations
const ZenSlider = ({ className, ...props }: React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>) => (
    <SliderPrimitive.Root
        className={cn(
            "relative flex w-full touch-none select-none items-center group cursor-pointer",
            className
        )}
        {...props}
    >
        <SliderPrimitive.Track className="relative h-1.5 w-full grow overflow-hidden rounded-full bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 shadow-inner cursor-pointer">
            <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-gray-600 to-black dark:from-gray-300 dark:to-white rounded-full shadow-sm" />
        </SliderPrimitive.Track>
        <SliderPrimitive.Thumb className="block h-5 w-5 rounded-full border-2 border-white dark:border-black bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 shadow-lg ring-0 transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-110 hover:shadow-xl group-hover:border-gray-300 dark:group-hover:border-gray-600 cursor-grab active:cursor-grabbing" />
    </SliderPrimitive.Root>
);

export function ControlPanel() {
    const {
        soundType,
        selectedVoiceModel,
        speed,
        stability,
        similarityBoost,
        styleExaggeration,
        durationSeconds,
        promptInfluence,
        setSpeed,
        setStability,
        setSimilarityBoost,
        setStyleExaggeration,
        setDurationSeconds,
        setPromptInfluence,
        resetSettings,
    } = useSoundStore();

    const { availableVoiceModels, selectVoiceModel } = useVoiceModels();
    const [isPlayingPreview, setIsPlayingPreview] = useState(false);

    const handlePlayPreview = useCallback(() => {
        if (!selectedVoiceModel) return;

        setIsPlayingPreview(true);
        // TODO: Implement voice preview playback
        setTimeout(() => {
            setIsPlayingPreview(false);
        }, 2000);
    }, [selectedVoiceModel]);

    return (
        <div className="w-72 bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800 overflow-y-auto">
            <div className="p-6 space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4">
                    <h2 className="text-sm font-normal text-black dark:text-white">Control</h2>
                    <button
                        onClick={resetSettings}
                        className="text-xs text-gray-700 dark:text-gray-300 hover:text-black dark:hover:text-white transition-colors"
                    >
                        Reset
                    </button>
                </div>

                {/* Voice Selection - Only for Text-to-Speech */}
                {soundType === 'text-to-speech' && (
                    <div className="space-y-4">
                        <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                            Voice
                        </label>
                        <div className="flex space-x-2">
                            <Select
                                value={selectedVoiceModel?.id || ""}
                                onValueChange={selectVoiceModel}
                            >
                                <SelectTrigger className="flex-1 h-9 bg-transparent border border-gray-200 dark:border-gray-600 text-sm font-light text-black dark:text-white hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                                    <SelectValue placeholder="Select voice" />
                                </SelectTrigger>
                                <SelectContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-600">
                                    {availableVoiceModels.map((model) => (
                                        <SelectItem
                                            key={model.id}
                                            value={model.id}
                                            className="text-sm font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
                                        >
                                            {model.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {selectedVoiceModel && (
                                <button
                                    onClick={handlePlayPreview}
                                    disabled={isPlayingPreview}
                                    className="h-9 w-9 flex items-center justify-center border border-gray-200 dark:border-gray-600 text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50 rounded-md"
                                >
                                    {isPlayingPreview ? (
                                        <Pause className="w-3 h-3" />
                                    ) : (
                                        <Play className="w-3 h-3" />
                                    )}
                                </button>
                            )}
                        </div>
                    </div>
                )}

                {/* Model Selection - Only for Text-to-Speech */}
                {soundType === 'text-to-speech' && (
                    <div className="space-y-4">
                        <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                            Model
                        </label>
                        <Select defaultValue="eleven-multilingual-v2">
                            <SelectTrigger className="w-full h-9 bg-transparent border border-gray-200 dark:border-gray-600 text-sm font-light text-black dark:text-white hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-600">
                                <SelectItem value="eleven-multilingual-v2" className="text-sm font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800">
                                    Eleven Multilingual v2
                                </SelectItem>
                                <SelectItem value="eleven-turbo-v2" className="text-sm font-light text-black dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800">
                                    Eleven Turbo v2
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                )}

                {/* Text-to-Speech Parameters */}
                {soundType === 'text-to-speech' && (
                    <>
                        {/* Speed Control */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                                    Speed
                                </label>
                                <span className="text-xs text-black dark:text-white font-mono">{speed.toFixed(1)}x</span>
                            </div>
                            <div className="space-y-3">
                                <ZenSlider
                                    value={[speed]}
                                    onValueChange={(value) => setSpeed(value[0])}
                                    min={0.7}
                                    max={1.2}
                                    step={0.1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300 font-mono">
                                    <span>0.7x</span>
                                    <span>1.2x</span>
                                </div>
                            </div>
                        </div>

                        {/* Stability Control */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                                    Stability
                                </label>
                                <span className="text-xs text-black dark:text-white font-mono">{stability.toFixed(2)}</span>
                            </div>
                            <div className="space-y-3">
                                <ZenSlider
                                    value={[stability]}
                                    onValueChange={(value) => setStability(value[0])}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                                    <span>0.0</span>
                                    <span>1.0</span>
                                </div>
                            </div>
                        </div>

                        {/* Similarity Boost Control */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                                    Similarity Boost
                                </label>
                                <span className="text-xs text-black dark:text-white font-mono">{similarityBoost.toFixed(2)}</span>
                            </div>
                            <div className="space-y-3">
                                <ZenSlider
                                    value={[similarityBoost]}
                                    onValueChange={(value) => setSimilarityBoost(value[0])}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                                    <span>0.0</span>
                                    <span>1.0</span>
                                </div>
                            </div>
                        </div>

                        {/* Style Exaggeration Control */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                                    Style Exaggeration
                                </label>
                                <span className="text-xs text-black dark:text-white font-mono">{styleExaggeration.toFixed(2)}</span>
                            </div>
                            <div className="space-y-3">
                                <ZenSlider
                                    value={[styleExaggeration]}
                                    onValueChange={(value) => setStyleExaggeration(value[0])}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                                    <span>0.0</span>
                                    <span>1.0</span>
                                </div>
                            </div>
                        </div>
                    </>
                )}

                {/* Sound Effect Parameters */}
                {soundType === 'sound-effect' && (
                    <>
                        {/* Duration Control */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                                    Duration
                                </label>
                                <span className="text-xs text-black dark:text-white font-mono">{durationSeconds.toFixed(1)}s</span>
                            </div>
                            <div className="space-y-3">
                                <ZenSlider
                                    value={[durationSeconds]}
                                    onValueChange={(value) => setDurationSeconds(value[0])}
                                    min={0.5}
                                    max={22}
                                    step={0.1}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300 font-mono">
                                    <span>0.5s</span>
                                    <span>22s</span>
                                </div>
                            </div>
                        </div>

                        {/* Prompt Influence Control */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-xs font-normal text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                                    Prompt Influence
                                </label>
                                <span className="text-xs text-black dark:text-white font-mono">{promptInfluence.toFixed(2)}</span>
                            </div>
                            <div className="space-y-3">
                                <ZenSlider
                                    value={[promptInfluence]}
                                    onValueChange={(value) => setPromptInfluence(value[0])}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-full"
                                />
                                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300">
                                    <span>0.0</span>
                                    <span>1.0</span>
                                </div>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
