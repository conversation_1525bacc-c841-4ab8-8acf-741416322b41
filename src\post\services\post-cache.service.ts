import { Injectable } from '@nestjs/common';
import { CustomLogger } from '../../common/services/logger.service';

/**
 * 帖子缓存服务
 * 用于缓存热度分数和其他计算结果，提升性能
 */
@Injectable()
export class PostCacheService {
    private readonly logger = new CustomLogger(PostCacheService.name);
    
    // 内存缓存存储
    private hotnessCache = new Map<string, { score: number; timestamp: number }>();
    private feedCache = new Map<string, { data: any; timestamp: number }>();
    
    // 缓存配置
    private readonly HOTNESS_CACHE_TTL = 5 * 60 * 1000; // 热度分数缓存5分钟
    private readonly FEED_CACHE_TTL = 2 * 60 * 1000; // Feed缓存2分钟
    private readonly MAX_CACHE_SIZE = 10000; // 最大缓存条目数

    /**
     * 获取帖子热度分数缓存
     * @param postId 帖子ID
     * @returns 缓存的热度分数，如果不存在或过期则返回null
     */
    getHotnessScore(postId: string): number | null {
        const cached = this.hotnessCache.get(postId);
        
        if (!cached) {
            return null;
        }
        
        // 检查是否过期
        if (Date.now() - cached.timestamp > this.HOTNESS_CACHE_TTL) {
            this.hotnessCache.delete(postId);
            return null;
        }
        
        return cached.score;
    }

    /**
     * 设置帖子热度分数缓存
     * @param postId 帖子ID
     * @param score 热度分数
     */
    setHotnessScore(postId: string, score: number): void {
        // 检查缓存大小，如果超过限制则清理旧数据
        if (this.hotnessCache.size >= this.MAX_CACHE_SIZE) {
            this.cleanupHotnessCache();
        }
        
        this.hotnessCache.set(postId, {
            score,
            timestamp: Date.now()
        });
    }

    /**
     * 批量设置热度分数缓存
     * @param scores 热度分数映射
     */
    setHotnessScores(scores: Map<string, number>): void {
        const timestamp = Date.now();
        
        for (const [postId, score] of scores) {
            if (this.hotnessCache.size >= this.MAX_CACHE_SIZE) {
                this.cleanupHotnessCache();
            }
            
            this.hotnessCache.set(postId, { score, timestamp });
        }
    }

    /**
     * 获取Feed缓存
     * @param cacheKey 缓存键
     * @returns 缓存的Feed数据，如果不存在或过期则返回null
     */
    getFeedCache(cacheKey: string): any | null {
        const cached = this.feedCache.get(cacheKey);
        
        if (!cached) {
            return null;
        }
        
        // 检查是否过期
        if (Date.now() - cached.timestamp > this.FEED_CACHE_TTL) {
            this.feedCache.delete(cacheKey);
            return null;
        }
        
        return cached.data;
    }

    /**
     * 设置Feed缓存
     * @param cacheKey 缓存键
     * @param data Feed数据
     */
    setFeedCache(cacheKey: string, data: any): void {
        // 检查缓存大小
        if (this.feedCache.size >= this.MAX_CACHE_SIZE) {
            this.cleanupFeedCache();
        }
        
        this.feedCache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 生成Feed缓存键
     * @param strategy 推荐策略
     * @param limit 限制数量
     * @param offset 偏移量
     * @param hotRatio 热门比例
     * @param tagId 标签ID
     * @returns 缓存键
     */
    generateFeedCacheKey(
        strategy: string,
        limit: number,
        offset: number,
        hotRatio?: number,
        tagId?: string
    ): string {
        const parts = [strategy, limit.toString(), offset.toString()];
        
        if (hotRatio !== undefined) {
            parts.push(hotRatio.toString());
        }
        
        if (tagId) {
            parts.push(tagId);
        }
        
        return `feed:${parts.join(':')}`;
    }

    /**
     * 清理过期的热度分数缓存
     */
    private cleanupHotnessCache(): void {
        const now = Date.now();
        const toDelete: string[] = [];
        
        for (const [key, value] of this.hotnessCache) {
            if (now - value.timestamp > this.HOTNESS_CACHE_TTL) {
                toDelete.push(key);
            }
        }
        
        // 如果过期的不够，删除最旧的一些条目
        if (toDelete.length < this.MAX_CACHE_SIZE * 0.1) {
            const entries = Array.from(this.hotnessCache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const deleteCount = Math.floor(this.MAX_CACHE_SIZE * 0.2);
            for (let i = 0; i < deleteCount && i < entries.length; i++) {
                toDelete.push(entries[i][0]);
            }
        }
        
        toDelete.forEach(key => this.hotnessCache.delete(key));
        
        this.logger.debug(`清理了 ${toDelete.length} 个热度分数缓存条目`);
    }

    /**
     * 清理过期的Feed缓存
     */
    private cleanupFeedCache(): void {
        const now = Date.now();
        const toDelete: string[] = [];
        
        for (const [key, value] of this.feedCache) {
            if (now - value.timestamp > this.FEED_CACHE_TTL) {
                toDelete.push(key);
            }
        }
        
        // 如果过期的不够，删除最旧的一些条目
        if (toDelete.length < this.MAX_CACHE_SIZE * 0.1) {
            const entries = Array.from(this.feedCache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const deleteCount = Math.floor(this.MAX_CACHE_SIZE * 0.2);
            for (let i = 0; i < deleteCount && i < entries.length; i++) {
                toDelete.push(entries[i][0]);
            }
        }
        
        toDelete.forEach(key => this.feedCache.delete(key));
        
        this.logger.debug(`清理了 ${toDelete.length} 个Feed缓存条目`);
    }

    /**
     * 清空所有缓存
     */
    clearAll(): void {
        this.hotnessCache.clear();
        this.feedCache.clear();
        this.logger.info('已清空所有缓存');
    }

    /**
     * 获取缓存统计信息
     * @returns 缓存统计
     */
    getCacheStats(): {
        hotnessCache: { size: number; ttl: number };
        feedCache: { size: number; ttl: number };
    } {
        return {
            hotnessCache: {
                size: this.hotnessCache.size,
                ttl: this.HOTNESS_CACHE_TTL
            },
            feedCache: {
                size: this.feedCache.size,
                ttl: this.FEED_CACHE_TTL
            }
        };
    }

    /**
     * 删除特定帖子的热度缓存（当帖子数据更新时调用）
     * @param postId 帖子ID
     */
    invalidateHotnessCache(postId: string): void {
        this.hotnessCache.delete(postId);
    }

    /**
     * 删除所有Feed缓存（当有新帖子发布时调用）
     */
    invalidateFeedCache(): void {
        this.feedCache.clear();
        this.logger.debug('已清空所有Feed缓存');
    }
}
