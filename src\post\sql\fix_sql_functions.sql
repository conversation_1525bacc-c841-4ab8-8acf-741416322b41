-- 修复SQL函数中的字段引用问题
-- 这个脚本重新创建简化版本的函数，避免字段引用歧义

-- 1. 删除现有函数（如果存在）
DROP FUNCTION IF EXISTS get_mixed_post_feed(INTEGER, INTEGER, NUMERIC);
DROP FUNCTION IF EXISTS get_hot_posts(INTEGER, INTEGER, INTEGER);
DROP FUNCTION IF EXISTS get_latest_posts(INTEGER, INTEGER);

-- 2. 重新创建热度计算函数（简化版本）
CREATE OR REPLACE FUNCTION calculate_post_hotness(
    p_click_count INTEGER DEFAULT 0,
    p_created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) RETURNS NUMERIC AS $$
DECLARE
    click_weight NUMERIC := 1.0;
    decay_lambda NUMERIC := 0.1;
    days_old NUMERIC;
    time_decay NUMERIC;
    base_score NUMERIC;
BEGIN
    -- 计算帖子发布天数
    days_old := EXTRACT(EPOCH FROM (NOW() - p_created_at)) / 86400.0;
    
    -- 计算时间衰减因子 (e^(-λ × days))
    time_decay := EXP(-decay_lambda * days_old);
    
    -- 计算基础分数（主要基于点击量）
    base_score := click_weight * p_click_count;
    
    -- 返回最终热度分数
    RETURN base_score * time_decay;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 3. 创建简化的混合Feed函数
CREATE OR REPLACE FUNCTION get_mixed_post_feed(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0,
    p_hot_ratio NUMERIC DEFAULT 0.7
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    title TEXT,
    description TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    task_id UUID,
    video_id UUID,
    like_count INTEGER,
    comment_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    visibility TEXT,
    is_deleted BOOLEAN,
    nsfw_level TEXT,
    ext JSONB,
    hotness_score NUMERIC,
    content_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id, 
        p.user_id, 
        p.title, 
        p.description, 
        p.video_url, 
        p.thumbnail_url,
        p.task_id, 
        p.video_id, 
        p.like_count, 
        p.comment_count, 
        p.created_at, 
        p.updated_at, 
        p.visibility, 
        p.is_deleted, 
        p.nsfw_level, 
        p.ext,
        calculate_post_hotness(
            COALESCE(ps.click_count, 0),
            p.created_at
        ) as hotness_score,
        CASE 
            WHEN p.created_at >= NOW() - INTERVAL '7 days' THEN 'new'
            ELSE 'hot'
        END::TEXT as content_type
    FROM public.posts p
    LEFT JOIN public.post_stats ps ON p.id = ps.post_id
    WHERE p.visibility = 'public' 
        AND p.is_deleted = false
    ORDER BY p.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 4. 创建热门帖子函数
CREATE OR REPLACE FUNCTION get_hot_posts(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0,
    p_days_range INTEGER DEFAULT 30
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    title TEXT,
    description TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    task_id UUID,
    video_id UUID,
    like_count INTEGER,
    comment_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    visibility TEXT,
    is_deleted BOOLEAN,
    nsfw_level TEXT,
    ext JSONB,
    hotness_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id, 
        p.user_id, 
        p.title, 
        p.description, 
        p.video_url, 
        p.thumbnail_url,
        p.task_id, 
        p.video_id, 
        p.like_count, 
        p.comment_count, 
        p.created_at, 
        p.updated_at, 
        p.visibility, 
        p.is_deleted, 
        p.nsfw_level, 
        p.ext,
        calculate_post_hotness(
            COALESCE(ps.click_count, 0),
            p.created_at
        ) as hotness_score
    FROM public.posts p
    LEFT JOIN public.post_stats ps ON p.id = ps.post_id
    WHERE p.visibility = 'public' 
        AND p.is_deleted = false
        AND p.created_at >= NOW() - (p_days_range || ' days')::INTERVAL
    ORDER BY calculate_post_hotness(COALESCE(ps.click_count, 0), p.created_at) DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 5. 创建最新帖子函数
CREATE OR REPLACE FUNCTION get_latest_posts(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    title TEXT,
    description TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    task_id UUID,
    video_id UUID,
    like_count INTEGER,
    comment_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    visibility TEXT,
    is_deleted BOOLEAN,
    nsfw_level TEXT,
    ext JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id, 
        p.user_id, 
        p.title, 
        p.description, 
        p.video_url, 
        p.thumbnail_url,
        p.task_id, 
        p.video_id, 
        p.like_count, 
        p.comment_count, 
        p.created_at, 
        p.updated_at, 
        p.visibility, 
        p.is_deleted, 
        p.nsfw_level, 
        p.ext
    FROM public.posts p
    WHERE p.visibility = 'public' 
        AND p.is_deleted = false
    ORDER BY p.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 6. 验证函数创建
DO $$
BEGIN
    RAISE NOTICE 'SQL functions have been recreated successfully';
    RAISE NOTICE 'Available functions:';
    RAISE NOTICE '  - calculate_post_hotness(click_count, created_at)';
    RAISE NOTICE '  - get_mixed_post_feed(limit, offset, hot_ratio)';
    RAISE NOTICE '  - get_hot_posts(limit, offset, days_range)';
    RAISE NOTICE '  - get_latest_posts(limit, offset)';
END $$;
