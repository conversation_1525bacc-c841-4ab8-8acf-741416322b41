import { IsNumber, IsString, IsIn } from "class-validator";
import { IsOptional } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GetPostFeedDto {
    @ApiPropertyOptional({
        description: '每页数量',
        default: 10,
        minimum: 1,
        maximum: 50
    })
    @IsOptional()
    @IsNumber()
    limit: number;

    @ApiPropertyOptional({
        description: '分页偏移量',
        default: 0,
        minimum: 0
    })
    @IsOptional()
    @IsNumber()
    offset: number;

    @ApiPropertyOptional({
        description: '标签ID，用于按标签筛选内容',
        example: 'uuid-string'
    })
    @IsOptional()
    @IsString()
    tag_id: string;

    @ApiPropertyOptional({
        description: 'Feed推荐策略',
        enum: ['mixed', 'hot', 'latest'],
        default: 'mixed',
        example: 'mixed'
    })
    @IsOptional()
    @IsString()
    @IsIn(['mixed', 'hot', 'latest'])
    strategy?: 'mixed' | 'hot' | 'latest';

    @ApiPropertyOptional({
        description: '混合策略中热门内容的占比（0.0-1.0）',
        default: 0.7,
        minimum: 0.0,
        maximum: 1.0
    })
    @IsOptional()
    @IsNumber()
    hot_ratio?: number;

    @ApiPropertyOptional({
        description: '热门内容的时间范围（天数）',
        default: 30,
        minimum: 1,
        maximum: 365
    })
    @IsOptional()
    @IsNumber()
    hot_days_range?: number;
}

