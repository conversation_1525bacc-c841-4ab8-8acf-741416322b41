-- 创建帖子表
create table public.posts (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  title text not null,
  description text null,
  video_url text not null,
  thumbnail_url text null,
  task_id uuid null,
  video_id uuid null,
  like_count integer not null default 0,
  comment_count integer not null default 0,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  visibility text not null default 'public'::text,
  is_deleted boolean not null default false,
  nsfw_level text null,
  ext jsonb not null default '{}'::jsonb,
  constraint posts_pkey primary key (id),
  constraint fk_task foreign KEY (task_id) references video_gen_tasks (id) on delete set null,
  constraint fk_video foreign KEY (video_id) references videos (id) on delete set null,
  constraint posts_user_id_fkey1 foreign KEY (user_id) references user_profiles (user_id)
) TABLESPACE pg_default;

-- 创建评论表
CREATE TABLE IF NOT EXISTS public.comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    content TEXT NOT NULL,
    parent_id UUID, -- 父评论ID，如果为空则为顶级评论
    target_type TEXT NOT NULL, -- 'post' 或 'model'
    target_id UUID NOT NULL, -- 帖子ID或模型ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    CONSTRAINT fk_user
        FOREIGN KEY(user_id)
        REFERENCES auth.users(id)
        ON DELETE CASCADE,
        
    CONSTRAINT fk_parent
        FOREIGN KEY(parent_id)
        REFERENCES public.comments(id)
        ON DELETE CASCADE
);

-- 创建点赞表
CREATE TABLE IF NOT EXISTS public.likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    post_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    CONSTRAINT fk_user
        FOREIGN KEY(user_id)
        REFERENCES auth.users(id)
        ON DELETE CASCADE,
        
    CONSTRAINT fk_post
        FOREIGN KEY(post_id)
        REFERENCES public.posts(id)
        ON DELETE CASCADE,
        
    -- 确保每个用户对每个帖子只能有一个点赞
    CONSTRAINT unique_user_post_like UNIQUE (user_id, post_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_posts_user_id ON public.posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON public.posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_posts_like_count ON public.posts(like_count DESC);

CREATE INDEX IF NOT EXISTS idx_comments_target ON public.comments(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);

CREATE INDEX IF NOT EXISTS idx_likes_post_id ON public.likes(post_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON public.likes(user_id);

-- 创建触发器函数，更新点赞数
CREATE OR REPLACE FUNCTION public.update_post_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.posts
        SET like_count = like_count + 1
        WHERE id = NEW.post_id;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.posts
        SET like_count = like_count - 1
        WHERE id = OLD.post_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建点赞触发器
CREATE TRIGGER update_post_like_count_trigger
AFTER INSERT OR DELETE ON public.likes
FOR EACH ROW EXECUTE FUNCTION public.update_post_like_count();

-- 创建触发器函数，更新评论数
CREATE OR REPLACE FUNCTION public.update_post_comment_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.target_type = 'post' THEN
            UPDATE public.posts
            SET comment_count = comment_count + 1
            WHERE id = NEW.target_id;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.target_type = 'post' THEN
            UPDATE public.posts
            SET comment_count = comment_count - 1
            WHERE id = OLD.target_id;
        END IF;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建评论触发器
CREATE TRIGGER update_post_comment_count_trigger
AFTER INSERT OR DELETE ON public.comments
FOR EACH ROW EXECUTE FUNCTION public.update_post_comment_count(); 