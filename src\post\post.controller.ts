import { Controller, Post, Get, Body, Param, Query, Req, UseGuards, BadRequestException, ParseUUIDPipe, DefaultValuePipe, ParseIntPipe } from '@nestjs/common';
import { PostService } from './post.service';
import { CreatePostDto } from './dto/create-post.dto';
import { CreateCommentDto } from './dto/comment.dto';
import { JwtGuard } from '../common/guards/jwt.guard';
import { Request } from 'express';
import { GetPostFeedDto } from './dto/feeds.dto';
import { LikePostDto } from './dto/like.dto';

@Controller('posts')
export class PostController {
    constructor(private readonly postService: PostService) {}

    /**
     * 创建帖子
     * @param req 请求对象，包含用户信息
     * @param createPostDto 创建帖子的参数
     */
    @Post()
    @UseGuards(JwtGuard)
    async createPost(
        @Req() req: Request,
        @Body() createPostDto: CreatePostDto,
    ) {
        const userId = req.user.id;
        if (!userId) {
            throw new BadRequestException('无效的用户信息');
        }

        return this.postService.createPost(userId, createPostDto);
    }

    /**
     * 获取帖子Feed流
     * @param req 请求对象，可包含用户信息
     * @param getPostFeedDto 包含分页参数和分类标签
     * - limit: 分页数量
     * - offset: 分页起始位置
     * - categoryTag: 可选的分类标签名称，如果提供则只返回包含该标签的帖子
     */
    @Post('feed')
    async getPostFeed(
        @Req() req: Request,
        @Body() getPostFeedDto: GetPostFeedDto,
    ) {
        const res =await this.postService.getPostFeed(getPostFeedDto);
        console.log(res)
        return res
    }

    /**
     * 获取用户的帖子列表
     * @param req 请求对象，可包含用户信息
     * @param userId 目标用户ID
     * @param limit 分页数量
     * @param offset 分页起始位置
     */
    @Get('user/:userId')
    @UseGuards(JwtGuard)
    async getUserPosts(
        @Req() req: Request,
        @Param('userId', ParseUUIDPipe) userId: string,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
        @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    ) {
        const currentUserId = req.user?.id || null;
        return this.postService.getUserPosts(userId, currentUserId, limit, offset);
    }

    /**
     * 获取帖子详情
     * @param req 请求对象，可包含用户信息
     * @param postId 帖子ID
     */
    @Get(':post_id')
    async getPostById(
        @Req() req: Request,
        @Param('post_id', ParseUUIDPipe) postId: string,
    ) {
        const userId = req.user?.id || null;
        return this.postService.getPostById(postId, userId);
    }

    /**
     * 点赞帖子
     * @param req 请求对象，包含用户信息
     * @param likePostDto 点赞帖子的参数
     */
    @Post('/like')
    @UseGuards(JwtGuard)
    async likePost(
        @Req() req: Request,
        @Body() likePostDto: LikePostDto,
    ) {
        const userId = req.user.id;
        if (!userId) {
            throw new BadRequestException('无效的用户信息');
        }

        return this.postService.likePost(likePostDto.post_id, userId, likePostDto.is_like);
    }

    /**
     * 创建评论
     * @param req 请求对象，包含用户信息
     * @param createCommentDto 创建评论的参数
     */
    @Post('comment')
    @UseGuards(JwtGuard)
    async createComment(
        @Req() req: Request,
        @Body() createCommentDto: CreateCommentDto,
    ) {
        const userId = req.user.id;
        if (!userId) {
            throw new BadRequestException('无效的用户信息');
        }

        return this.postService.createComment(userId, createCommentDto);
    }

    /**
     * 获取帖子的评论列表
     * @param postId 帖子ID
     * @param parentId 父评论ID，如果获取回复评论
     * @param limit 分页数量
     * @param offset 分页起始位置
     */
    @Get(':post_id/comments')
    async getPostComments(
        @Param('post_id', ParseUUIDPipe) postId: string,
        @Query('parentId') parentId: string,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
        @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    ) {
        return this.postService.getComments('post', postId, parentId || null, limit, offset);
    }

    /**
     * 获取模型的评论列表
     * @param modelId 模型ID
     * @param parentId 父评论ID，如果获取回复评论
     * @param limit 分页数量
     * @param offset 分页起始位置
     */
    @Get('model/:model_id/comments')
    async getModelComments(
        @Param('model_id', ParseUUIDPipe) modelId: string,
        @Query('parentId') parentId: string,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
        @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    ) {
        return this.postService.getComments('model', modelId, parentId || null, limit, offset);
    }
} 