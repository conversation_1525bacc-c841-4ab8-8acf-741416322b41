import { Test, TestingModule } from '@nestjs/testing';
import { PostCacheService } from './post-cache.service';
import { CustomLogger } from '../../common/services/logger.service';

describe('PostCacheService', () => {
  let service: PostCacheService;
  let logger: CustomLogger;

  beforeEach(async () => {
    const mockLogger = {
      setContext: jest.fn(),
      log: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostCacheService,
        {
          provide: CustomLogger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<PostCacheService>(PostCacheService);
    logger = module.get<CustomLogger>(CustomLogger);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('热度分数缓存', () => {
    it('应该能够设置和获取热度分数', () => {
      const postId = 'test-post-id';
      const score = 15.6;

      // 设置缓存
      service.setHotnessScore(postId, score);

      // 获取缓存
      const cachedScore = service.getHotnessScore(postId);
      expect(cachedScore).toBe(score);
    });

    it('应该在缓存过期后返回null', async () => {
      const postId = 'test-post-id';
      const score = 15.6;

      // 设置缓存
      service.setHotnessScore(postId, score);

      // 模拟缓存过期（通过修改时间戳）
      const cache = (service as any).hotnessCache;
      const entry = cache.get(postId);
      entry.timestamp = Date.now() - 6 * 60 * 1000; // 6分钟前

      // 获取缓存应该返回null
      const cachedScore = service.getHotnessScore(postId);
      expect(cachedScore).toBeNull();
    });

    it('应该能够批量设置热度分数', () => {
      const scores = new Map([
        ['post1', 10.5],
        ['post2', 20.3],
        ['post3', 5.8],
      ]);

      service.setHotnessScores(scores);

      // 验证所有分数都被正确设置
      expect(service.getHotnessScore('post1')).toBe(10.5);
      expect(service.getHotnessScore('post2')).toBe(20.3);
      expect(service.getHotnessScore('post3')).toBe(5.8);
    });

    it('应该能够清除特定帖子的热度缓存', () => {
      const postId = 'test-post-id';
      const score = 15.6;

      service.setHotnessScore(postId, score);
      expect(service.getHotnessScore(postId)).toBe(score);

      service.invalidateHotnessCache(postId);
      expect(service.getHotnessScore(postId)).toBeNull();
    });
  });

  describe('Feed缓存', () => {
    it('应该能够设置和获取Feed缓存', () => {
      const cacheKey = 'test-feed-key';
      const feedData = { posts: [], total: 0 };

      service.setFeedCache(cacheKey, feedData);

      const cachedData = service.getFeedCache(cacheKey);
      expect(cachedData).toEqual(feedData);
    });

    it('应该在缓存过期后返回null', () => {
      const cacheKey = 'test-feed-key';
      const feedData = { posts: [], total: 0 };

      service.setFeedCache(cacheKey, feedData);

      // 模拟缓存过期
      const cache = (service as any).feedCache;
      const entry = cache.get(cacheKey);
      entry.timestamp = Date.now() - 3 * 60 * 1000; // 3分钟前

      const cachedData = service.getFeedCache(cacheKey);
      expect(cachedData).toBeNull();
    });

    it('应该能够生成正确的缓存键', () => {
      const key1 = service.generateFeedCacheKey('mixed', 10, 0);
      expect(key1).toBe('feed:mixed:10:0');

      const key2 = service.generateFeedCacheKey('hot', 20, 10, 0.7);
      expect(key2).toBe('feed:hot:20:10:0.7');

      const key3 = service.generateFeedCacheKey('mixed', 10, 0, 0.8, 'tag-123');
      expect(key3).toBe('feed:mixed:10:0:0.8:tag-123');
    });

    it('应该能够清空所有Feed缓存', () => {
      service.setFeedCache('key1', { posts: [], total: 0 });
      service.setFeedCache('key2', { posts: [], total: 0 });

      expect(service.getFeedCache('key1')).not.toBeNull();
      expect(service.getFeedCache('key2')).not.toBeNull();

      service.invalidateFeedCache();

      expect(service.getFeedCache('key1')).toBeNull();
      expect(service.getFeedCache('key2')).toBeNull();
    });
  });

  describe('缓存统计', () => {
    it('应该返回正确的缓存统计信息', () => {
      // 添加一些缓存数据
      service.setHotnessScore('post1', 10.5);
      service.setFeedCache('feed1', { posts: [], total: 0 });

      const stats = service.getCacheStats();

      expect(stats).toHaveProperty('hotnessCache');
      expect(stats).toHaveProperty('feedCache');
      expect(stats.hotnessCache.size).toBeGreaterThan(0);
      expect(stats.feedCache.size).toBeGreaterThan(0);
      expect(typeof stats.hotnessCache.ttl).toBe('number');
      expect(typeof stats.feedCache.ttl).toBe('number');
    });
  });

  describe('缓存清理', () => {
    it('应该能够清空所有缓存', () => {
      service.setHotnessScore('post1', 10.5);
      service.setFeedCache('feed1', { posts: [], total: 0 });

      service.clearAll();

      expect(service.getHotnessScore('post1')).toBeNull();
      expect(service.getFeedCache('feed1')).toBeNull();
      expect(logger.log).toHaveBeenCalledWith('已清空所有缓存');
    });
  });
});
