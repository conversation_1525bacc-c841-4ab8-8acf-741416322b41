"use client";

import { useState, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SoundTask } from "@/store/useSoundStore";
import { useSoundHistory } from "../hooks/useSoundHistory";
import {
    Play,
    Pause,
    Download,
    Copy,
    MoreHorizontal,
    Loader2,
    History
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";



interface HistoryCardProps {
    task: SoundTask;
    onPlay: (task: SoundTask) => void;
    onDownload: (task: SoundTask) => void;
    onCopyText: (text: string) => void;
    isPlaying: boolean;
}

function HistoryCard({ task, onPlay, onDownload, onCopyText, isPlaying }: HistoryCardProps) {
    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));

        if (diffMins < 1) return "now";
        if (diffMins < 60) return `${diffMins}m`;
        if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h`;
        return date.toLocaleDateString();
    };

    const getStatusIndicator = () => {
        switch (task.status) {
            case "processing":
                return <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse" />;
            case "failed":
                return <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />;
            default:
                return null;
        }
    };

    return (
        <div className="group py-6 border-b border-gray-100 dark:border-gray-900 last:border-b-0 hover:bg-gray-50/30 dark:hover:bg-gray-950/30 transition-colors">
            {/* Header - Status and Time */}
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                    {getStatusIndicator()}
                    <span className="text-xs text-gray-400 font-mono">{formatTime(task.created_at)}</span>
                </div>

                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                            <MoreHorizontal className="w-3 h-3" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 shadow-lg">
                        <DropdownMenuItem
                            onClick={() => onCopyText(task.text)}
                            className="text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900"
                        >
                            <Copy className="w-3 h-3 mr-2" />
                            Copy
                        </DropdownMenuItem>
                        {task.status === "completed" && task.audio_url && (
                            <DropdownMenuItem
                                onClick={() => onDownload(task)}
                                className="text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900"
                            >
                                <Download className="w-3 h-3 mr-2" />
                                Download
                            </DropdownMenuItem>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {/* Text Content */}
            <div className="text-sm text-gray-900 dark:text-gray-100 leading-relaxed mb-4 line-clamp-3">
                {task.text}
            </div>

            {/* Voice Settings */}
            <div className="text-xs text-gray-500 font-mono mb-4 space-x-3">
                <span>{task.voice_model_name}</span>
                <span>•</span>
                <span>{task.speed}x</span>
                <span>•</span>
                <span>{(task.stability * 100).toFixed(0)}%</span>
            </div>

            {/* Error Message */}
            {task.status === "failed" && task.error_message && (
                <div className="text-xs text-gray-500 mb-4 p-3 bg-gray-50 dark:bg-gray-950 rounded border-l-2 border-gray-300 dark:border-gray-700">
                    {task.error_message}
                </div>
            )}

            {/* Play Button */}
            {task.status === "completed" && task.audio_url && (
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onPlay(task)}
                    className="h-8 px-3 text-xs bg-transparent border border-gray-200 dark:border-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-950 hover:border-gray-300 dark:hover:border-gray-700 transition-colors"
                >
                    {isPlaying ? (
                        <>
                            <Pause className="w-3 h-3 mr-1.5" />
                            Pause
                        </>
                    ) : (
                        <>
                            <Play className="w-3 h-3 mr-1.5" />
                            Play
                        </>
                    )}
                </Button>
            )}
        </div>
    );
}

export function HistoryPanel() {
    const { soundHistory, isLoadingHistory, hasMoreHistory, loadMoreHistory } = useSoundHistory();
    const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
    const audioRef = useRef<HTMLAudioElement | null>(null);

    const handlePlay = useCallback((task: SoundTask) => {
        if (!task.audio_url) return;

        if (currentlyPlaying === task.id) {
            // Pause current audio
            if (audioRef.current) {
                audioRef.current.pause();
            }
            setCurrentlyPlaying(null);
        } else {
            // Play new audio
            if (audioRef.current) {
                audioRef.current.pause();
            }

            // Create new audio element
            const audio = new Audio(task.audio_url);
            audioRef.current = audio;

            audio.onended = () => {
                setCurrentlyPlaying(null);
            };

            audio.onerror = () => {
                setCurrentlyPlaying(null);
            };

            audio.play().then(() => {
                setCurrentlyPlaying(task.id);
            }).catch(() => {
                setCurrentlyPlaying(null);
            });
        }
    }, [currentlyPlaying]);

    const handleDownload = useCallback((task: SoundTask) => {
        if (!task.audio_url) return;

        const link = document.createElement('a');
        link.href = task.audio_url;
        link.download = `voice-${task.id}.mp3`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, []);

    const handleCopyText = useCallback((text: string) => {
        navigator.clipboard.writeText(text);
    }, []);

    return (
        <div className="w-80 bg-white dark:bg-black border-l border-gray-100 dark:border-gray-900 flex flex-col">
            {/* Header */}
            <div className="px-6 py-5 border-b border-gray-100 dark:border-gray-900">
                <h2 className="text-sm font-medium text-gray-900 dark:text-gray-100 tracking-wide">History</h2>
            </div>

            {/* History List */}
            <div className="flex-1 overflow-y-auto px-6">
                {soundHistory.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-16 text-center">
                        <div className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center mb-4">
                            <History className="w-4 h-4 text-gray-400" />
                        </div>
                        <p className="text-sm text-gray-500 mb-1">No generations yet</p>
                        <p className="text-xs text-gray-400">Your voice history will appear here</p>
                    </div>
                ) : (
                    <div className="py-2">
                        {soundHistory.map((task) => (
                            <HistoryCard
                                key={task.id}
                                task={task}
                                onPlay={handlePlay}
                                onDownload={handleDownload}
                                onCopyText={handleCopyText}
                                isPlaying={currentlyPlaying === task.id}
                            />
                        ))}
                    </div>
                )}

                {/* Load More Button */}
                {hasMoreHistory && (
                    <div className="py-4 border-t border-gray-100 dark:border-gray-900">
                        <Button
                            variant="ghost"
                            size="sm"
                            className="w-full h-8 text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-950"
                            disabled={isLoadingHistory}
                            onClick={loadMoreHistory}
                        >
                            {isLoadingHistory ? (
                                <>
                                    <Loader2 className="w-3 h-3 mr-2 animate-spin" />
                                    Loading
                                </>
                            ) : (
                                "Load more"
                            )}
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
}
