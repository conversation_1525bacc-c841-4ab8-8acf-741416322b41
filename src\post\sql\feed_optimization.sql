-- Feed流优化SQL脚本
-- 为混合推荐算法添加必要的索引和函数

-- 1. 添加复合索引以支持热度和时间混合查询
CREATE INDEX IF NOT EXISTS idx_posts_visibility_created_at_desc 
ON public.posts(visibility, created_at DESC) 
WHERE visibility = 'public' AND is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_posts_visibility_like_count_desc 
ON public.posts(visibility, like_count DESC) 
WHERE visibility = 'public' AND is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_posts_visibility_comment_count_desc 
ON public.posts(visibility, comment_count DESC) 
WHERE visibility = 'public' AND is_deleted = false;

-- 2. 为post_stats表添加索引
CREATE INDEX IF NOT EXISTS idx_post_stats_click_count_desc 
ON public.post_stats(click_count DESC);

CREATE INDEX IF NOT EXISTS idx_post_stats_view_count_desc 
ON public.post_stats(view_count DESC);

-- 3. 创建热度计算函数
CREATE OR REPLACE FUNCTION calculate_post_hotness(
    p_click_count INTEGER DEFAULT 0,
    p_like_count INTEGER DEFAULT 0,
    p_comment_count INTEGER DEFAULT 0,
    p_created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) RETURNS NUMERIC AS $$
DECLARE
    click_weight NUMERIC := 1.0;
    like_weight NUMERIC := 3.0;
    comment_weight NUMERIC := 5.0;
    decay_lambda NUMERIC := 0.1;
    days_old NUMERIC;
    time_decay NUMERIC;
    base_score NUMERIC;
BEGIN
    -- 计算帖子发布天数
    days_old := EXTRACT(EPOCH FROM (NOW() - p_created_at)) / 86400.0;
    
    -- 计算时间衰减因子 (e^(-λ × days))
    time_decay := EXP(-decay_lambda * days_old);
    
    -- 计算基础分数
    base_score := (click_weight * p_click_count + 
                   like_weight * p_like_count + 
                   comment_weight * p_comment_count);
    
    -- 返回最终热度分数
    RETURN base_score * time_decay;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 4. 创建获取混合Feed的函数
CREATE OR REPLACE FUNCTION get_mixed_post_feed(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0,
    p_hot_ratio NUMERIC DEFAULT 0.7  -- 热门内容占比，默认70%
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    title TEXT,
    description TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    task_id UUID,
    video_id UUID,
    like_count INTEGER,
    comment_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    visibility TEXT,
    is_deleted BOOLEAN,
    nsfw_level TEXT,
    ext JSONB,
    hotness_score NUMERIC,
    content_type TEXT  -- 'hot' 或 'new'
) AS $$
DECLARE
    hot_limit INTEGER;
    new_limit INTEGER;
BEGIN
    -- 计算热门和新内容的数量分配
    hot_limit := FLOOR(p_limit * p_hot_ratio);
    new_limit := p_limit - hot_limit;
    
    -- 返回混合结果
    RETURN QUERY
    (
        -- 热门内容：基于热度分数排序
        SELECT 
            p.id, p.user_id, p.title, p.description, p.video_url, p.thumbnail_url,
            p.task_id, p.video_id, p.like_count, p.comment_count, p.created_at, 
            p.updated_at, p.visibility, p.is_deleted, p.nsfw_level, p.ext,
            calculate_post_hotness(
                COALESCE(ps.click_count, 0),
                p.like_count,
                p.comment_count,
                p.created_at
            ) as hotness_score,
            'hot'::TEXT as content_type
        FROM public.posts p
        LEFT JOIN public.post_stats ps ON p.id = ps.post_id
        WHERE p.visibility = 'public' 
            AND p.is_deleted = false
            AND p.created_at >= NOW() - INTERVAL '30 days'  -- 只考虑30天内的内容
        ORDER BY hotness_score DESC
        LIMIT hot_limit
        OFFSET FLOOR(p_offset * p_hot_ratio)
    )
    UNION ALL
    (
        -- 新内容：基于发布时间排序
        SELECT 
            p.id, p.user_id, p.title, p.description, p.video_url, p.thumbnail_url,
            p.task_id, p.video_id, p.like_count, p.comment_count, p.created_at, 
            p.updated_at, p.visibility, p.is_deleted, p.nsfw_level, p.ext,
            calculate_post_hotness(
                COALESCE(ps.click_count, 0),
                p.like_count,
                p.comment_count,
                p.created_at
            ) as hotness_score,
            'new'::TEXT as content_type
        FROM public.posts p
        LEFT JOIN public.post_stats ps ON p.id = ps.post_id
        WHERE p.visibility = 'public' 
            AND p.is_deleted = false
            AND p.created_at >= NOW() - INTERVAL '7 days'  -- 新内容：7天内
        ORDER BY p.created_at DESC
        LIMIT new_limit
        OFFSET FLOOR(p_offset * (1 - p_hot_ratio))
    )
    ORDER BY 
        CASE 
            WHEN content_type = 'hot' THEN hotness_score 
            ELSE EXTRACT(EPOCH FROM created_at) 
        END DESC;
END;
$$ LANGUAGE plpgsql;

-- 5. 创建获取纯热门内容的函数（用于热门标签页）
CREATE OR REPLACE FUNCTION get_hot_posts(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0,
    p_days_range INTEGER DEFAULT 30  -- 考虑多少天内的内容
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    title TEXT,
    description TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    task_id UUID,
    video_id UUID,
    like_count INTEGER,
    comment_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    visibility TEXT,
    is_deleted BOOLEAN,
    nsfw_level TEXT,
    ext JSONB,
    hotness_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id, p.user_id, p.title, p.description, p.video_url, p.thumbnail_url,
        p.task_id, p.video_id, p.like_count, p.comment_count, p.created_at, 
        p.updated_at, p.visibility, p.is_deleted, p.nsfw_level, p.ext,
        calculate_post_hotness(
            COALESCE(ps.click_count, 0),
            p.like_count,
            p.comment_count,
            p.created_at
        ) as hotness_score
    FROM public.posts p
    LEFT JOIN public.post_stats ps ON p.id = ps.post_id
    WHERE p.visibility = 'public' 
        AND p.is_deleted = false
        AND p.created_at >= NOW() - (p_days_range || ' days')::INTERVAL
    ORDER BY hotness_score DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 6. 创建获取最新内容的函数
CREATE OR REPLACE FUNCTION get_latest_posts(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    title TEXT,
    description TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    task_id UUID,
    video_id UUID,
    like_count INTEGER,
    comment_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    visibility TEXT,
    is_deleted BOOLEAN,
    nsfw_level TEXT,
    ext JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id, p.user_id, p.title, p.description, p.video_url, p.thumbnail_url,
        p.task_id, p.video_id, p.like_count, p.comment_count, p.created_at, 
        p.updated_at, p.visibility, p.is_deleted, p.nsfw_level, p.ext
    FROM public.posts p
    WHERE p.visibility = 'public' 
        AND p.is_deleted = false
    ORDER BY p.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;
