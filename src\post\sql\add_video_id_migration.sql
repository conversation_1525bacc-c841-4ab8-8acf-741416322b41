-- 添加video_id字段到posts表的迁移脚本
-- 这个脚本用于修复现有数据库结构

-- 1. 添加video_id字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'video_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.posts ADD COLUMN video_id UUID NULL;
        
        -- 添加外键约束
        ALTER TABLE public.posts 
        ADD CONSTRAINT fk_video 
        FOREIGN KEY (video_id) REFERENCES public.videos (id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added video_id column to posts table';
    ELSE
        RAISE NOTICE 'video_id column already exists in posts table';
    END IF;
END $$;

-- 2. 尝试从task_id关联填充video_id字段（如果数据存在）
DO $$
BEGIN
    -- 更新posts表，通过task_id关联videos表来填充video_id
    UPDATE public.posts 
    SET video_id = v.id
    FROM public.videos v
    WHERE posts.task_id = v.task_id 
    AND posts.video_id IS NULL
    AND posts.task_id IS NOT NULL;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % posts with video_id from task_id relationship', updated_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not update video_id from task_id: %', SQLERRM;
END $$;

-- 3. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_posts_video_id ON public.posts(video_id);

-- 4. 验证数据完整性
DO $$
DECLARE
    total_posts INTEGER;
    posts_with_video_id INTEGER;
    posts_with_task_id INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_posts FROM public.posts WHERE is_deleted = false;
    SELECT COUNT(*) INTO posts_with_video_id FROM public.posts WHERE video_id IS NOT NULL AND is_deleted = false;
    SELECT COUNT(*) INTO posts_with_task_id FROM public.posts WHERE task_id IS NOT NULL AND is_deleted = false;
    
    RAISE NOTICE 'Data integrity check:';
    RAISE NOTICE '  Total active posts: %', total_posts;
    RAISE NOTICE '  Posts with video_id: %', posts_with_video_id;
    RAISE NOTICE '  Posts with task_id: %', posts_with_task_id;
    
    IF posts_with_video_id < posts_with_task_id THEN
        RAISE WARNING 'Some posts have task_id but no video_id. This may indicate missing video records.';
    END IF;
END $$;
